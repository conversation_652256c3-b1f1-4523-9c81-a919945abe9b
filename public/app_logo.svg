<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" fill="none">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0f766e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#134e4a;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="bookGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f8fafc;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="8" stdDeviation="16" flood-color="#000000" flood-opacity="0.25"/>
    </filter>
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMerge in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>

  <!-- App icon background with rounded square -->
  <rect x="64" y="64" width="896" height="896" rx="180" ry="180" fill="url(#bgGradient)"/>

  <!-- Inner highlight -->
  <rect x="80" y="80" width="864" height="864" rx="164" ry="164" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="4"/>

  <!-- Main book - Large and prominent -->
  <rect x="200" y="350" width="624" height="400" rx="32" fill="url(#bookGradient)" filter="url(#shadow)"/>

  <!-- Book spine - Bold -->
  <rect x="200" y="350" width="64" height="400" rx="16" fill="#0f766e"/>
  <rect x="216" y="366" width="32" height="368" rx="8" fill="#14b8a6" opacity="0.6"/>

  <!-- Page content lines - Bold and clear -->
  <line x1="320" y1="420" x2="720" y2="420" stroke="#475569" stroke-width="12"/>
  <line x1="320" y1="480" x2="680" y2="480" stroke="#475569" stroke-width="10"/>
  <line x1="320" y1="540" x2="700" y2="540" stroke="#475569" stroke-width="10"/>
  <line x1="320" y1="600" x2="660" y2="600" stroke="#475569" stroke-width="10"/>
  <line x1="320" y1="660" x2="690" y2="660" stroke="#475569" stroke-width="10"/>

  <!-- Shield protection - Reduced height -->
  <path d="M512 200 L620 250 L620 400 Q620 480 512 540 Q404 480 404 400 L404 250 Z"
        fill="#10b981" stroke="#ffffff" stroke-width="8" filter="url(#shadow)"/>

  <!-- Shield inner glow -->
  <path d="M512 230 L590 270 L590 390 Q590 450 512 500 Q434 450 434 390 L434 270 Z"
        fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="4"/>

  <!-- Large bold checkmark -->
  <path d="M460 360 L500 400 L570 310"
        fill="none" stroke="#ffffff" stroke-width="24" stroke-linecap="round" stroke-linejoin="round"/>

  <!-- Security lock symbol -->
  <circle cx="512" cy="320" r="16" fill="none" stroke="#ffffff" stroke-width="6"/>
  <rect x="500" y="328" width="24" height="18" rx="6" fill="#ffffff"/>

  <!-- Corner security accents -->
  <path d="M160 160 L200 160 L200 200" fill="none" stroke="rgba(255,255,255,0.4)" stroke-width="6" stroke-linecap="round"/>
  <path d="M864 160 L824 160 L824 200" fill="none" stroke="rgba(255,255,255,0.4)" stroke-width="6" stroke-linecap="round"/>
  <path d="M160 864 L200 864 L200 824" fill="none" stroke="rgba(255,255,255,0.4)" stroke-width="6" stroke-linecap="round"/>
  <path d="M864 864 L824 864 L824 824" fill="none" stroke="rgba(255,255,255,0.4)" stroke-width="6" stroke-linecap="round"/>
</svg>
