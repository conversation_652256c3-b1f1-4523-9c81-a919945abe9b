mod searching;
mod activate;
mod assemble;
mod system_feature;
mod book;
mod card;
mod catalogue;
mod dictionary;
mod filter_data;
pub mod increment;
pub mod file_upload;
pub mod websocket;
pub mod maps;
pub mod law;
mod static_files;
mod fe_dist;
mod plagiarism;
use std::thread;
use actix_web::{web, App, HttpServer};
use tokio::runtime::Runtime;
use crate::controller::activate::{ctrl_activate_parse_code, ctrl_get_activate_key, ctrl_get_activate_load_status, ctrl_get_activate_status, ctrl_get_un_activate_info};
use crate::controller::assemble::ctrl_get_assembles_by_type;
use crate::controller::book::{ctrl_get_book_by_book_id, ctrl_get_book_news_info_by_brand_pub_date, ctrl_get_book_page_by_types};
use crate::controller::card::{ctrl_get_card_content_by_book_id_card_id, ctrl_get_card_infos_by_book_id};
use crate::controller::catalogue::{ctrl_get_catalogues_by_book_id, ctrl_get_newspaper_catalogue_by_book_id};
use crate::controller::dictionary::{ctrl_get_all_radicals, ctrl_get_dictionary, ctrl_get_letter_by_id, ctrl_get_letter_sections_by_radical_id, ctrl_get_letters_by_stroke_count, ctrl_get_letters_with_pinyin_by_without_tone, ctrl_get_pinyin_without_tones, ctrl_get_snapshots, ctrl_get_vocabulary_by_id, ctrl_search_resources};
use crate::controller::fe_dist::fe_dist_ctrl_config;
use crate::controller::file_upload::{ctrl_merge_file_chunks, ctrl_upload_file_chunk};
use crate::controller::filter_data::book_filter::{ctrl_filter_authors, ctrl_filter_book_types, ctrl_filter_books, ctrl_filter_grades, ctrl_filter_series, ctrl_filter_subjects, ctrl_filter_versions};
use crate::controller::increment::{ctrl_delete_increment_record_by_id, ctrl_get_increment_result, ctrl_import_increments_queue};
use crate::controller::law::{ctrl_get_law, ctrl_get_law_total};
use crate::controller::maps::{ctrl_get_maps, ctrl_get_maps_bread_crumb, ctrl_get_maps_filters, ctrl_get_maps_total};
use crate::controller::searching::{ctrl_global_search_books, ctrl_global_search_cards};
use crate::controller::static_files::ctrl_static_file;
use crate::controller::system_feature::{ctrl_get_system_feat_details, ctrl_set_user_data_dir_custom_config};
use crate::controller::websocket::broadcast_ws;
use crate::controller::plagiarism::{
    plagiarism_events_sse, ctrl_plagiarism_compare, ctrl_plagiarism_results,
    ctrl_plagiarism_batches, ctrl_plagiarism_batch_detail, ctrl_plagiarism_batch_statistics,
    ctrl_plagiarism_delete_batch, ctrl_plagiarism_cancel_comparison
};
use crate::services::system_feature::{SystemFeature, UsageMode};

pub async fn startup_web_server() -> Result<(), Box<dyn std::error::Error>> {
    let usage_mode: UsageMode = SystemFeature::get_sys_feat_owned()?;
    if !usage_mode.is_server() {
        return Ok(());
    }
    let port = usage_mode.port;

    let _handle = thread::spawn(move || {
        // Create a new Tokio runtime
        let runtime = Runtime::new().unwrap();
        let _rst: Result<(), Box<dyn std::error::Error>> = runtime.block_on(async {
            HttpServer::new(move || {
                App::new()
                    .service(web::resource("/ws-broadcast").route(web::get().to(broadcast_ws)))
                    .route("/static/{filename:.*}", web::get().to(ctrl_static_file))
                    .route("/api/activate/load", web::get().to(ctrl_get_activate_load_status))
                    .route("/api/activate/status", web::get().to(ctrl_get_activate_status))
                    .route("/api/activate/info", web::get().to(ctrl_get_un_activate_info))
                    .route("/api/activate/key", web::get().to(ctrl_get_activate_key))
                    .route("/api/activate/parseCode", web::get().to(ctrl_activate_parse_code))
                    .route("/api/increment/result", web::get().to(ctrl_get_increment_result))
                    .route("/api/increment/import-increments-queue", web::post().to(ctrl_import_increments_queue))
                    .route("/api/increment/record/delete/by-id", web::get().to(ctrl_delete_increment_record_by_id))
                    .route("/api/upload/chunk", web::post().to(ctrl_upload_file_chunk))
                    .route("/api/upload/chunks-merge", web::post().to(ctrl_merge_file_chunks))
                    .route("/api/assemble/by-book-type", web::get().to(ctrl_get_assembles_by_type))
                    .route("/api/system-feature/details", web::get().to(ctrl_get_system_feat_details))
                    .route("/api/system-feature/dir-custom-config", web::post().to(ctrl_set_user_data_dir_custom_config))
                    .route("/api/global-search/cards", web::post().to(ctrl_global_search_cards))
                    .route("/api/global-search/books", web::post().to(ctrl_global_search_books))
                    .route("/api/book/by-id", web::get().to(ctrl_get_book_by_book_id))
                    .route("/api/book/news-info/by-brand-pub-date", web::post().to(ctrl_get_book_news_info_by_brand_pub_date))
                    .route("/api/book/page/by-types", web::post().to(ctrl_get_book_page_by_types))
                    .route("/api/card/infos/by-book-id", web::get().to(ctrl_get_card_infos_by_book_id))
                    .route("/api/card/content/by-book-id-card-id", web::post().to(ctrl_get_card_content_by_book_id_card_id))
                    .route("/api/catalogue/by-book-id", web::get().to(ctrl_get_catalogues_by_book_id))
                    .route("/api/catalogue/newspaper/by-book-id", web::get().to(ctrl_get_newspaper_catalogue_by_book_id))
                    .route("/api/dictionary/by-id", web::get().to(ctrl_get_dictionary))
                    .route("/api/dictionary/without-tones", web::get().to(ctrl_get_pinyin_without_tones))
                    .route("/api/dictionary/le-w-py/by-wt", web::get().to(ctrl_get_letters_with_pinyin_by_without_tone))
                    .route("/api/dictionary/letter/by-id", web::get().to(ctrl_get_letter_by_id))
                    .route("/api/dictionary/vocabulary/by-id", web::get().to(ctrl_get_vocabulary_by_id))
                    .route("/api/dictionary/all-radicals", web::get().to(ctrl_get_all_radicals))
                    .route("/api/dictionary/letter-secs/by-radical-id", web::get().to(ctrl_get_letter_sections_by_radical_id))
                    .route("/api/dictionary/letters-by-stroke-count", web::get().to(ctrl_get_letters_by_stroke_count))
                    .route("/api/dictionary/snapshots", web::get().to(ctrl_get_snapshots))
                    .route("/api/dictionary/resources/search", web::post().to(ctrl_search_resources))
                    .route("/api/book-filter/book-types", web::post().to(ctrl_filter_book_types))
                    .route("/api/book-filter/subjects", web::post().to(ctrl_filter_subjects))
                    .route("/api/book-filter/versions", web::post().to(ctrl_filter_versions))
                    .route("/api/book-filter/grades", web::post().to(ctrl_filter_grades))
                    .route("/api/book-filter/series", web::post().to(ctrl_filter_series))
                    .route("/api/book-filter/authors", web::post().to(ctrl_filter_authors))
                    .route("/api/book-filter/books", web::post().to(ctrl_filter_books))
                    .route("/api/maps", web::post().to(ctrl_get_maps))
                    .route("/api/maps/getMapsBreadCrumb", web::get().to(ctrl_get_maps_bread_crumb))
                    .route("/api/maps/getMapsFilters", web::post().to(ctrl_get_maps_filters))
                    .route("/api/maps/getMapsTotal", web::get().to(ctrl_get_maps_total))
                    .route("/api/laws", web::post().to(ctrl_get_law))
                    .route("/api/laws/getLawsTotal", web::get().to(ctrl_get_law_total))
                    // Plagiarism API routes
                    .route("/api/plagiarism/events", web::get().to(plagiarism_events_sse))
                    .route("/api/plagiarism/compare", web::post().to(ctrl_plagiarism_compare))
                    .route("/api/plagiarism/results", web::post().to(ctrl_plagiarism_results))
                    .route("/api/plagiarism/batches", web::post().to(ctrl_plagiarism_batches))
                    .route("/api/plagiarism/batch/{batch_id}", web::get().to(ctrl_plagiarism_batch_detail))
                    .route("/api/plagiarism/batch/{batch_id}/statistics", web::get().to(ctrl_plagiarism_batch_statistics))
                    .route("/api/plagiarism/batch/{batch_id}", web::delete().to(ctrl_plagiarism_delete_batch))
                    .route("/api/plagiarism/batch/{batch_id}/cancel", web::post().to(ctrl_plagiarism_cancel_comparison))
                    // 注意，以下为了反向代理前端打包静态文件，可能需要放到最后，建议在它上面添加接口配置
                    .service(web::scope("").configure(fe_dist_ctrl_config))
            })
                .bind(("0.0.0.0", port))?
                .run()
                .await.expect("TODO: panic message");
            Ok(())
        });
    });

    Ok(())
}