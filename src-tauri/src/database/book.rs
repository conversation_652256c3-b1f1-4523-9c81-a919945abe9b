use std::error::Error;
use crate::database::{create_surreal_db_index, IndexType, Pagination};
use crate::database::surreal::get_db;
use crate::models::book::{Book,QueryDate, QueryNews};
use crate::services::PageResult;

pub(crate) struct BookRepository;

pub async fn get_book_news_info_by_brand_pub_date(
    params: QueryNews
) -> Result<Vec<QueryDate>, Box<dyn Error>> {
    println!("{:?}",params.clone());
    let QueryNews{
        brand,
        publish_date,
        types
    } = params;
    let db= get_db();
    let date: Vec<QueryDate>;
    if types == 1 {
        let mut next_date_ = db
            .query(format!(
                "SELECT publishDate,bookId FROM book WHERE brand = '{}' and publishDate > '{}' ORDER BY publishDate limit 1",
                brand,publish_date
            ))
            .await?;
        date = next_date_.take(0)?;
    } else if types == 0 {
        let mut pre_date_ = db
            .query(format!(
                "SELECT publishDate,bookId FROM book WHERE brand = '{}' and publishDate < '{}' ORDER BY publishDate DESC limit 1",
                brand,publish_date
            ))
            .await?;
        date = pre_date_.take(0)?;
    } else  {
        let mut date_ = db
            .query(format!(
                "SELECT publishDate,bookId FROM book WHERE brand = '{}' and publishDate == '{} 00:00:00'",
                brand,publish_date
            ))
            .await?;
        date = date_.take(0)?;
    }

    Ok(date)
}

pub async fn get_book_by_book_id(book_id: String) -> Result<Vec<Book>, Box<dyn Error>> {
    let db= get_db();
    let mut resp = db
        .query(format!(
            "SELECT * FROM book WHERE bookId = {}",
            book_id
        ))
        .await?;
    let opt_book: Vec<Book> = resp.take(0)?;
    Ok(opt_book)
}
impl BookRepository {
    pub(crate) async fn create_indexes() -> Result<(), Box<dyn Error>> {
        let db= get_db();
        create_surreal_db_index(&db, "book", vec![vec!["bookId"]], IndexType::Unique).await?;
        create_surreal_db_index(
            &db, "book",
            vec![
                vec!["type"], vec!["brand"],
                vec!["brand", "publishDate"]
            ],
            IndexType::Normal
        ).await?;
        Ok(())
    }

    pub(crate) async fn find_by_id(id: u64) -> Result<Option<Book>, Box<dyn Error>> {
        let db= get_db();
        let mut resp = db
            .query("SELECT * FROM book WHERE bookId = $id")
            .bind(("id", id))
            .await?;
        let opt_book: Option<Book> = resp.take(0)?;
        Ok(opt_book)
    }

    pub(crate) async fn find_all() -> Result<Vec<Book>, Box<dyn Error>> {
        let db= get_db();
        let sql = "SELECT * FROM book";
        let mut resp = db.query(sql).await?;
        let list: Vec<Book> = resp.take(0)?;
        Ok(list)
    }

    pub(crate) async fn find_all_by_ids(ids: &Vec<u64>) -> Result<Vec<Book>, Box<dyn Error>> {
        let db= get_db();
        let temp: Vec<String> = ids.iter().map(|id| id.to_string()).collect();
        let sql = format!("SELECT * FROM book WHERE bookId IN [{}]", temp.join(","));
        let mut resp = db.query(sql).await?;
        let list: Vec<Book> = resp.take(0)?;
        Ok(list)
    }

    pub(crate) async fn count_by_type_in(
        search_text: &str, types: &Vec<String>
    ) -> Result<usize, Box<dyn Error>> {
        let db= get_db();
        let temp = types.iter()
            .map(|t| format!("\'{}\'", t))
            .collect::<Vec<String>>()
            .join(",");
        let mut sql = "COUNT(SELECT * FROM book".to_string();
        let mut wheres = vec![];
        if !temp.is_empty() {
            wheres.push(format!("type IN[{}]", temp));
        }
        if !search_text.trim().is_empty() {
            wheres.push(format!("bookName CONTAINS \'{}\'", search_text.trim()));
        }
        if !wheres.is_empty() {
            sql.push_str(" WHERE ");
            sql.push_str(&wheres.join(" AND "));
        }
        sql.push_str(")");
        let mut resp = db.query(sql).await?;
        let count: Option<usize> = resp.take(0)?;
        Ok(count.unwrap())
    }

    pub(crate) async fn page_by_type_in(
        search_text: &str,
        types: &Vec<String>, page: Pagination
    ) -> Result<PageResult<Book>, Box<dyn Error>> {
        let count = Self::count_by_type_in(search_text, types).await?;
        let db= get_db();
        let temp = types.iter()
            .map(|t| format!("\'{}\'", t))
            .collect::<Vec<String>>()
            .join(",");
        let mut sql = "SELECT * FROM book".to_string();
        let mut wheres = vec![];
        if !temp.is_empty() {
            wheres.push(format!("type IN[{}]", temp));
        }
        if !search_text.trim().is_empty() {
            wheres.push(format!("bookName CONTAINS \'{}\'", search_text.trim()));
        }
        if !wheres.is_empty() {
            sql.push_str(" WHERE ");
            sql.push_str(&wheres.join(" AND "));
        }
        let page_no = page.opt_page_no.unwrap_or(0);
        let page_size = page.opt_page_size.unwrap_or(25);
        let start = page_no *page_size;
        sql.push_str(" LIMIT ");
        sql.push_str(&page_size.to_string());
        sql.push_str(" START ");
        sql.push_str(&start.to_string());
        let mut resp = db.query(sql).await?;
        let list: Vec<Book> = resp.take(0)?;
        Ok(PageResult::new(count, page_no, page_size, list))
    }

    pub(crate) async fn save(book: &Book) -> Result<(), Box<dyn Error>> {
        let existing_record = Self::find_by_id(book.book_id).await?;
        let db= get_db();
        if existing_record.is_none() {
            let _: Option<Book> = db.create("book").content(book.clone()).await?;
        } else {
            let id = existing_record.unwrap().id.unwrap();
            let _r: Option<Book> = db.update(id).content(book.clone()).await?;
        }
        Ok(())
    }

    pub(crate) async fn save_all(books: &Vec<Book>) -> Result<(), Box<dyn Error>> {
        for book in books {
            Self::save(book).await?;
        }
        Ok(())
    }

    pub(crate) async fn find_by_book_id_in_order_by_publish_date(
        book_ids: &Vec<u64>, pub_date_asc: bool
    ) -> Result<Option<Book>, Box<dyn Error>> {
        let order_by_rule = if pub_date_asc { "ASC" } else { "DESC" };
        let temp = book_ids.iter()
            .map(|b| b.to_string())
            .collect::<Vec<String>>()
            .join(",");
        let sql = format!(
            "SELECT * FROM book WHERE bookId IN [{}] AND publishDate IS NOT None ORDER BY publishDate {} LIMIT 1",
            temp, order_by_rule
        );
        let db= get_db();
        let mut result = db.query(sql).await?;
        let list: Vec<Book> = result.take(0)?;
        Ok(list.first().cloned())
    }
}