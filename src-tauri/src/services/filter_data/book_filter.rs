use std::collections::HashSet;
use std::fs;
use std::hash::{<PERSON>h, <PERSON><PERSON>};
use std::path::Path;
use tokio::sync::RwLock;
use lazy_static::lazy_static;
use serde_derive::{Deserialize, Serialize};
use crate::database::book::BookRepository;
use crate::models::book::Book;
use crate::models::ResponseVO;
use crate::utils::path::get_book_summary_file_path;


#[derive(Serialize, Deserialize, Clone, Debug)]
pub(crate) struct BookFilterParams {
    #[serde(rename = "type")]
    pub opt_type: Option<String>,
    #[serde(rename = "period")]
    pub opt_period: Option<i32>,
    #[serde(rename = "subjectCode")]
    pub opt_subject_code: Option<String>,
    #[serde(rename = "version")]
    pub opt_version: Option<String>,
    #[serde(rename = "grade")]
    pub opt_grade: Option<String>,
    #[serde(rename = "author")]
    pub opt_author: Option<String>,
    #[serde(rename = "series")]
    pub opt_series: Option<String>,
}

#[derive(Serialize, Deserialize, Clone, Debug, Eq, PartialEq, Hash)]
pub(crate) struct Subject {
    pub name: String,
    pub code: String,
}

#[derive(Serialize, Deserialize, Clone, Debug)]
pub(crate) struct BookInfo {
    #[serde(rename = "bookId")]
    pub book_id: u64,
    #[serde(rename = "bookName")]
    pub book_name: String,
}

#[derive(Serialize, Deserialize, Debug, Clone, Eq)]
struct BookSummary {
    book_id: u64,
    book_name: String,
    author: Option<String>,
    series: Option<String>,
    subject_name: String,
    subject_code: String,
    r#type: String,
    version: Option<String>,
    period: Option<i32>,
    grade: Option<String>,
}

impl PartialEq for BookSummary {
    fn eq(&self, other: &Self) -> bool {
        self.book_id == other.book_id
    }
}

impl Hash for BookSummary {
    fn hash<H: Hasher>(&self, state: &mut H) {
        self.book_id.hash(state);
    }
}

impl BookInfo {
    fn from(book_smy_list: Vec<BookSummary>) -> Vec<Self> {
        book_smy_list
            .into_iter()
            .map(|s| Self { book_id: s.book_id, book_name: s.book_name })
            .collect()
    }
}

pub(crate) struct BookFilter;

lazy_static! {
    static ref G_BOOK_SUMMARY_SET: RwLock<Option<HashSet<BookSummary>>> = RwLock::new(None);
}

impl BookFilter {
    fn summary_set(books: &Vec<Book>) -> HashSet<BookSummary> {
        let mut set = HashSet::new();

        for b in books.iter() {
            set.insert(BookSummary {
                book_id: b.book_id,
                book_name: b.book_name.clone(),
                author: b.author.clone(),
                series: b.series.clone(),
                subject_name: b.subject_name.clone(),
                subject_code: b.subject_code.clone(),
                r#type: b.r#type.clone(),
                version: b.version.clone(),
                period: b.period,
                grade: b.grade.clone(),
            });
        }

        set
    }

    async fn check_cache(
        bs_set_file_path: &Path
    ) -> Result<(), Box<dyn std::error::Error>> {
        let exists = {
            let opt_bs_set = G_BOOK_SUMMARY_SET.read().await;
            opt_bs_set.is_some()
        };
        if !exists {
            let mut temp_set = HashSet::new();
            let mut need_resave = true;
            if bs_set_file_path.exists() {
                let buffer = fs::read(bs_set_file_path)?;
                let result = bincode::serde::decode_from_slice(&buffer, bincode::config::legacy());
                if result.is_ok() {
                    need_resave = false;
                    temp_set = result.unwrap().0;
                }
            }
            if need_resave {
                let all_books = BookRepository::find_all().await?;
                temp_set = Self::summary_set(&all_books);

                let serialized = bincode::serde::encode_to_vec(&temp_set, bincode::config::legacy())?;
                fs::write(bs_set_file_path, serialized)?;
            }
            let mut opt_bs_set = G_BOOK_SUMMARY_SET.write().await;
            *opt_bs_set = Some(temp_set);
        }

        Ok(())
    }

    pub(crate) async fn reset() {
        let mut temp_set = G_BOOK_SUMMARY_SET.write().await;
        *temp_set = None;
    }

    pub(crate) async fn add_books(
        bs_set_file_path: &Path, books: &Vec<Book>
    ) -> Result<(), Box<dyn std::error::Error>> {
        Self::check_cache(bs_set_file_path).await?;
        let set = Self::summary_set(books);
        let mut opt_bs_set = G_BOOK_SUMMARY_SET.write().await;
        if opt_bs_set.is_none() {
            let temp_set = if bs_set_file_path.exists() {
                let buffer = fs::read(bs_set_file_path)?;
                let result = bincode::serde::decode_from_slice(&buffer, bincode::config::legacy());
                if let Ok((ts, _)) = result {
                    ts
                } else {
                    let all_books = BookRepository::find_all().await?;
                    Self::summary_set(&all_books)
                }
            } else {
                let all_books = BookRepository::find_all().await?;
                Self::summary_set(&all_books)
            };
            *opt_bs_set = Some(temp_set);
        }
        opt_bs_set.as_mut().unwrap().extend(set);
        let serialized = bincode::serde::encode_to_vec(opt_bs_set.as_ref().unwrap(), bincode::config::legacy())?;
        fs::write(bs_set_file_path, serialized)?;
        Ok(())
    }

    async fn filter(
        params: BookFilterParams,
    ) -> Result<HashSet<BookSummary>, Box<dyn std::error::Error>> {
        let bs_set_path_buf = get_book_summary_file_path()?;
        let bs_set_path = bs_set_path_buf.as_path();
        Self::check_cache(bs_set_path).await?;
        let opt_bs_set = G_BOOK_SUMMARY_SET.read().await;
        let bs_set = opt_bs_set.as_ref().unwrap();
        let mut target_set = HashSet::new();
        for bs in bs_set.iter() {
            let mut shot = true;
            if let Some(ref r#type) = params.opt_type {
                if bs.r#type.as_str() != r#type {
                    shot = false;
                }
            }
            if shot {
                if let Some(period) = params.opt_period {
                    if bs.period.is_none() || bs.period.unwrap() != period {
                        shot = false;
                    }
                }
            }
            if shot {
                if let Some(ref subject_code) = params.opt_subject_code {
                    if bs.subject_code.as_str() != subject_code {
                        shot = false;
                    }
                }
            }
            if shot {
                if let Some(ref version) = params.opt_version {
                    if bs.version.is_none() || bs.version.as_ref().unwrap() != version {
                        shot = false;
                    }
                }
            }
            if shot {
                if let Some(ref grade) = params.opt_grade {
                    if bs.grade.is_none() || bs.grade.as_ref().unwrap() != grade {
                        shot = false;
                    }
                }
            }
            if shot {
                if let Some(ref author) = params.opt_author {
                    if bs.author.is_none() || bs.author.as_ref().unwrap() != author {
                        shot = false;
                    }
                }
            }
            if shot {
                if let Some(ref series) = params.opt_series {
                    if bs.series.is_none() || bs.series.as_ref().unwrap() != series {
                        shot = false;
                    }
                }
            }
            if shot {
                target_set.insert(bs.clone());
            }
        }
        Ok(target_set)
    }

    pub(crate) async fn subjects(
        params: BookFilterParams,
    ) -> Result<ResponseVO<Vec<Subject>>, ResponseVO<()>> {
        let target_set = Self::filter(params).await
            .map_err(|e| ResponseVO::error(Some(e.to_string())))?;
        let s_set: HashSet<Subject> = target_set.into_iter()
            .map(|bs| Subject { name: bs.subject_name, code: bs.subject_code })
            .collect();
        let mut subjects = vec![];
        subjects.extend(s_set);
        Ok(ResponseVO::success(Some(subjects), None))
    }

    pub(crate) async fn versions(
        params: BookFilterParams,
    ) -> Result<ResponseVO<Vec<String>>, ResponseVO<()>> {
        let target_set = Self::filter(params).await
            .map_err(|e| ResponseVO::error(Some(e.to_string())))?;
        let set: HashSet<String> = target_set.into_iter()
            .filter(|bs| bs.version.is_some())
            .map(|bs| bs.version.unwrap())
            .filter(|v| !v.is_empty())
            .collect();
        let mut versions = vec![];
        versions.extend(set);
        Ok(ResponseVO::success(Some(versions), None))
    }

    pub(crate) async fn grades(
        params: BookFilterParams,
    ) -> Result<ResponseVO<Vec<String>>, ResponseVO<()>> {
        let target_set = Self::filter(params).await
            .map_err(|e| ResponseVO::error(Some(e.to_string())))?;
        let set: HashSet<String> = target_set.into_iter()
            .filter(|bs| bs.grade.is_some())
            .map(|bs| bs.grade.unwrap())
            .filter(|v| !v.is_empty())
            .collect();
        let mut grades = vec![];
        grades.extend(set);
        Ok(ResponseVO::success(Some(grades), None))
    }

    pub(crate) async fn series(
        params: BookFilterParams,
    ) -> Result<ResponseVO<Vec<String>>, ResponseVO<()>> {
        let target_set = Self::filter(params).await
            .map_err(|e| ResponseVO::error(Some(e.to_string())))?;
        let set: HashSet<String> = target_set.into_iter()
            .filter(|bs| bs.series.is_some())
            .map(|bs| bs.series.unwrap())
            .filter(|v| !v.is_empty())
            .collect();
        let mut series = vec![];
        series.extend(set);
        Ok(ResponseVO::success(Some(series), None))
    }

    pub(crate) async fn authors(
        params: BookFilterParams,
    ) -> Result<ResponseVO<Vec<String>>, ResponseVO<()>> {
        let target_set = Self::filter(params).await
            .map_err(|e| ResponseVO::error(Some(e.to_string())))?;
        let set: HashSet<String> = target_set.into_iter()
            .filter(|bs| bs.author.is_some())
            .map(|bs| bs.author.unwrap())
            .filter(|v| !v.is_empty())
            .collect();
        let mut authors = vec![];
        authors.extend(set);
        Ok(ResponseVO::success(Some(authors), None))
    }

    pub(crate) async fn book_types(
        params: BookFilterParams,
    ) -> Result<ResponseVO<Vec<String>>, ResponseVO<()>> {
        let target_set = Self::filter(params).await
            .map_err(|e| ResponseVO::error(Some(e.to_string())))?;
        let set: HashSet<String> = target_set.into_iter()
            .filter(|bs| !bs.r#type.is_empty())
            .map(|bs| bs.r#type.clone())
            .collect();
        let mut types = vec![];
        types.extend(set);
        Ok(ResponseVO::success(Some(types), None))
    }

    pub(crate) async fn books(
        params: BookFilterParams,
    ) -> Result<ResponseVO<Vec<BookInfo>>, ResponseVO<()>> {
        let target_set = Self::filter(params).await
            .map_err(|e| ResponseVO::error(Some(e.to_string())))?;
        let mut books = vec![];
        books.extend(target_set);
        books.sort_by(|a, b| a.book_id.cmp(&b.book_id));
        Ok(ResponseVO::success(Some(BookInfo::from(books)), None))
    }

}

