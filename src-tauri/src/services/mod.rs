use serde_derive::{Deserialize, Serialize};

pub(crate) mod system_feature;
pub(crate) mod increment;
pub(crate) mod book;
pub(crate) mod catalogue;
pub(crate) mod assemble;
pub(crate) mod card;
pub(crate) mod filter_data;
pub(crate) mod dictionary;
pub(crate) mod file_url;
pub mod maps;
pub(crate) mod inc_file_upd;
pub mod law;
pub(crate) mod backup_recover;
pub(crate) mod plagiarism;
#[derive(Debug, Serialize, Deserialize)]
pub struct PageResult<T> {
    totals: usize,
    #[serde(rename = "pageNo")]
    page_no: usize,
    #[serde(rename = "pageCount")]
    page_count: usize,
    #[serde(rename = "pageSize")]
    page_size: usize,
    pub list: Vec<T>,
}

impl<T> PageResult<T> {
    pub fn new(totals: usize, page_no: usize, page_size: usize, list: Vec<T>) -> Self {
        let mut page_count = totals / page_size;
        if totals % page_size != 0 { page_count += 1;  }
        Self {
            totals,
            page_no,
            page_count,
            page_size,
            list,
        }
    }
}

