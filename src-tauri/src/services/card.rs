use std::collections::HashMap;
use crate::database::{card};
use crate::database::card::CardRepository;
use crate::models::card::{Card, CardInfo, Context, QueryNewspaperContext};
use crate::models::ResponseVO;

pub async fn get_card_infos_by_book_id(
    book_id: u64
) -> Result<ResponseVO<Vec<CardInfo>>, ResponseVO<()>> {
    let cards = CardRepository::find_all_by_book_id_order_by_page_serial(book_id).await
        .map_err(|e| ResponseVO::error(Some(e.to_string())))?;
    let mut page_card_map = HashMap::new();
    for c in cards.into_iter() {
        page_card_map.entry(c.page.unwrap_or(0))
            .or_insert(Vec::new())
            .push(c);
    }
    let mut infos = vec![];
    for (page, cs) in page_card_map {
        let text = cs.into_iter()
            .map(|c| c.content.trim().to_string())
            .collect::<Vec<_>>()
            .join("\n");
        infos.push(CardInfo {
            page,
            text,
        });
    }
    infos.sort_by(|a, b| a.page.cmp(&b.page));

    Ok(ResponseVO::success(Some(infos), None))
}

pub async fn get_card_content_by_book_id_card_id(
    params: QueryNewspaperContext
) -> Result<ResponseVO<Vec<Context>>, ResponseVO<()>> {
    match card::get_card_content_by_book_id_card_id(params).await {
        Ok(list) => Ok(ResponseVO::success(Some(list), None)),
        Err(error) => Err(ResponseVO::error(Some(error.to_string()))),
    }
}

#[allow(unused)]
pub async fn get_cards_by_book_id(
    book_id: u64
) -> Result<ResponseVO<Vec<Card>>, ResponseVO<()>> {
    let cards = CardRepository::find_all_by_book_id_order_by_page_serial(book_id).await
        .map_err(|e| ResponseVO::error(Some(e.to_string())))?;
    Ok(ResponseVO::success(Some(cards), None))
}
