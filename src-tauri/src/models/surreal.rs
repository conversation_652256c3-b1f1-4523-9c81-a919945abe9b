use lazy_static::lazy_static;
use serde::{Deserialize, Deserializer};
use serde_derive::Deserialize as DeserializeDerive;
use serde_json::{Map, Value};
use std::sync::Mutex;
use surrealdb::engine::local::{Db};
use surrealdb::sql::Thing;
use surrealdb::Surreal;
#[allow(unused)]
const SDB_PATH: &str = "surrealDB";

#[derive(Debug, DeserializeDerive)]
pub struct Record {
    #[allow(dead_code)]
    id: Thing,
}

pub fn deserialize_id_u64<'de, D>(deserializer: D) -> Result<u64, D::Error>
where
    D: Deserializer<'de>,
{
    let value: Value = Deserialize::deserialize(deserializer)?;
    match value {
        Value::Number(n) => n
            .as_u64()
            .ok_or_else(|| serde::de::Error::custom("id转换失败：Expected a u64 value")),
        Value::String(s) => s
            .parse::<u64>()
            .map_err(|_| serde::de::Error::custom("id转换失败：Invalid u64 string")),
        Value::Object(map) => {
            if let Some(id_value) = map.get("id") {
                // !(id_value.clone());
                match id_value {
                    Value::Object(inner_map) => {
                        if let Some(inner_value) = inner_map.get("Number") {
                            match inner_value {
                                Value::Number(n) => n.as_u64().ok_or_else(|| serde::de::Error::custom("id转换失败：Expected a u64 value in the nested map")),
                                _ => Err(serde::de::Error::custom("id转换失败：Expected 'Number' to be a string or number in the nested map")),
                            }
                        } else if let Some(inner_value) = inner_map.get("String") {
                            match inner_value {
                                Value::String(s) => s.parse::<u64>().map_err(|_| serde::de::Error::custom("id转换失败：Invalid u64 string in the nested map")),
                                _ => Err(serde::de::Error::custom("id转换失败：Expected 'String' to be a string or number in the nested map")),
                            }
                        } else {
                            Err(serde::de::Error::custom(
                                "id转换失败：Expected 'Number' in the nested map",
                            ))
                        }
                    }
                    _ => Err(serde::de::Error::custom(
                        "id转换失败：Expected 'id' to be a string or number in the map",
                    )),
                }
            } else {
                Err(serde::de::Error::custom(
                    "id转换失败：Expected 'id' in the map",
                ))
            }
        }
        _ => Err(serde::de::Error::custom(
            "id转换失败：Expected a string, number, or map for ID",
        )),
    }
}
#[allow(unused)]
pub fn deserialize_id_string<'de, D>(deserializer: D) -> Result<String, D::Error>
where
    D: Deserializer<'de>,
{
    let value: Value = Deserialize::deserialize(deserializer)?;
    match value {
        Value::Number(n) => Ok(n.to_string()),
        Value::String(s) => Ok(s),
        Value::Object(map) => {
            if let Some(id_value) = map.get("id") {

                match id_value {
                    Value::Object(inner_map) => {
                        if let Some(inner_value) = inner_map.get("String") {
                            match inner_value {
                                Value::String(s) => Ok(s.clone()),
                                _ => Err(serde::de::Error::custom("id转换失败：Expected 'String' to be a string or number in the nested map")),
                            }
                        } else if let Some(inner_value) = inner_map.get("Number") {
                            match inner_value {
                                Value::Number(n) => Ok(n.to_string()),
                                _ => Err(serde::de::Error::custom("id转换失败：Expected 'Number' to be a string or number in the nested map")),
                            }
                        } else {
                            Err(serde::de::Error::custom(
                                "id转换失败：Expected 'Number' in the nested map",
                            ))
                        }
                    }
                    _ => Err(serde::de::Error::custom(
                        "id转换失败：Expected 'id' to be a string or number in the map",
                    )),
                }
            } else {
                Err(serde::de::Error::custom("Expected 'id' in the map"))
            }
        }
        Value::Null => Ok("".to_string()),
        _ => Err(serde::de::Error::custom(
            "Expected a string, number, or map for ID",
        )),
    }
}
#[allow(unused)]
pub fn deserialize_id_string_option<'de, D>(deserializer: D) -> Result<Option<String>, D::Error>
where
    D: Deserializer<'de>,
{
    // Otherwise, call the original deserialize_id_string method
    let string_value: String = deserialize_id_string(deserializer)?;
    Ok(Some(string_value))
}

lazy_static! {
    static ref DB: Mutex<Option<Surreal<Db>>> = Mutex::new(None);
}
#[allow(unused)]
pub async fn get_db() -> Surreal<Db> {
    let db_option = DB.lock().unwrap();
    let db = db_option.clone().unwrap();
    db
}

pub fn get_update_map(value: Value) -> Map<String, Value> {
    get_update_map_specify_id_field(value, "id")
}

pub fn get_update_map_specify_id_field(value: Value, id_field_name: &str) -> Map<String, Value> {
    let mut map: Map<String, Value> = Default::default();
    if let Some(obj) = value.as_object() {
        map = obj.clone();
        map.remove(id_field_name); // Remove the "id" field
    } else {
        println!("Failed to convert the task to a map");
    }
    map
}