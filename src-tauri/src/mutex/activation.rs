use crate::models::activation::{Activation, License, ParseCodeParams};
use chrono::{Local, NaiveDate};
use once_cell::sync::Lazy;
use rand::Rng;
use tokio::sync::RwLock;

static LICENSE: Lazy<RwLock<Option<License>>> = Lazy::new(|| RwLock::new(None));
static ACTIVATION: Lazy<RwLock<Option<Activation>>> = Lazy::new(|| RwLock::new(None));

pub async fn init_activation() -> Result<(), String> {
    // 初始化 License
    let l = License::load_from_db().await;
    {
        let mut license_option = LICENSE.write().await;
        *license_option = Some(l);
    }

    // 获取并初始化 Activation
    let key = get_init_key().await;
    let expiration = get_deadline().await;
    let a = Activation::new(key, expiration).await;
    {
        let mut activation_option = ACTIVATION.write().await;
        *activation_option = Some(a);
    }
    Ok(())
}

async fn get_license() -> Option<License> {
    LICENSE.read().await.clone()
}

async fn get_activation() -> Option<Activation> {
    ACTIVATION.read().await.clone()
}

async fn get_init_key() -> String {
    let license = get_license().await.unwrap();
    match license.cd_key {
        Some(cd_key) if !cd_key.is_empty() => cd_key,
        _ => rand::rng().random_range(0..10000).to_string(),
    }
}

async fn is_same_cdk() -> bool {
    let cdk_o = get_license().await.unwrap().cd_key;
    if cdk_o.is_none() {
        return false;
    }
    let cdk = cdk_o.unwrap();
    get_activation().await.unwrap().is_same_key(cdk.parse().unwrap())
}

async fn in_valid_time() -> bool {
    get_activation().await.unwrap().valid()
}

async fn get_deadline() -> NaiveDate {
    let license = get_license().await.unwrap();
    match license.activation {
        Some(exp) if !exp.is_empty() => {
            NaiveDate::parse_from_str(exp.as_str(), "%Y-%m-%d").unwrap_or(Local::now().date_naive())
        }
        _ => Local::now().date_naive(),
    }
}

pub async fn is_activate_load() -> bool {
    get_license().await.is_some() && get_activation().await.is_some()
}

/// 获取激活信息
pub async fn un_activate_info() -> String {
    if !is_activated().await {
        if !is_same_cdk().await {
            return String::from("设备未激活");
        }
        if !in_valid_time().await {
            return String::from("设备激活已过期");
        }
    }
    String::from("设备已激活")
}

/// 判读是否激活
pub async fn is_activated() -> bool {
    is_same_cdk().await && in_valid_time().await
}


/// 输入一个code，判断是否合法激活，返回激活状态
pub async fn parse_code(params: ParseCodeParams) -> bool {
    let code = params.code.as_str();
    let mut activation = get_activation().await.unwrap();
    let value = activation.parse_code(code, true).await;
    if value {
        let res = init_activation().await;
        return res.is_ok();
    }
    value
}
/// 获取设备码
pub async fn machine_key() -> String {
    format!("{:0>6}", get_activation().await.unwrap().get_key())
}

pub async fn generate_usable_code() -> bool {
    let activation = get_activation().await.unwrap();
    let code = activation.gen_code(activation.get_key(), 1);
    log::warn!("{}", code);
    true
}