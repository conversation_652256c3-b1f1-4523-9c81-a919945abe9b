{"build": {"beforeDevCommand": "vite --mode dev", "beforeBuildCommand": "vite build --mode prod", "frontendDist": "./resources/dist", "devUrl": "http://localhost:1420"}, "bundle": {"active": true, "targets": "all", "windows": {"certificateThumbprint": null, "digestAlgorithm": "sha256", "timestampUrl": "", "wix": {"language": "zh-CN", "fragmentPaths": ["wix.fragment.wxs"]}, "nsis": {"displayLanguageSelector": true, "languages": ["SimpChinese"], "installMode": "currentUser"}}, "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "resources": ["resources/**/*"], "linux": {"appimage": {"bundleMediaFramework": true}}}, "productName": "预出版查重", "mainBinaryName": "Book-Guard", "version": "1.0.0", "identifier": "com.qctchina.book-guard", "plugins": {}, "app": {"windows": [{"fullscreen": false, "resizable": true, "maximizable": true, "zoomHotkeysEnabled": true, "title": "预出版查重", "minWidth": 1280, "minHeight": 800}], "security": {"assetProtocol": {"scope": ["**", "asset://localhost/", "http://asset.localhost/", "/home/<USER>/.local/share/**"], "enable": true}, "csp": {"default-src": "'self' customprotocol: asset: asset: blob: data: filesystem: ws: wss: http: https: tauri: 'unsafe-eval' 'unsafe-inline'", "connect-src": "ipc: http://ipc.localhost http://tauri.localhost http://asset.localhost http:", "font-src": ["https://fonts.gstatic.com", "http://tauri.localhost", "data:"], "img-src": "'self' asset: http://asset.localhost blob: data: *", "style-src": "'unsafe-inline' 'self' https://fonts.googleapis.com", "media-src": "'self' asset: https://asset.localhost http://asset.localhost"}}}}