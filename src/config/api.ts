// API configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080';

export { API_BASE_URL };

// Configure axios defaults
import axios from 'axios';

axios.defaults.baseURL = API_BASE_URL;
axios.defaults.timeout = 30000;

// Add request interceptor for error handling
axios.interceptors.response.use(
  (response) => response,
  (error) => {
      console.error('API Error:', error);
      return Promise.reject(error);
  }
);
