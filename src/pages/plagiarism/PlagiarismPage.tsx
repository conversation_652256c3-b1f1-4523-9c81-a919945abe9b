import React, { useState, useEffect } from 'react';
import { Card, Tabs, Typography, Space, Button, message } from 'antd';
import {
    BookOutlined,
    BarChartOutlined,
    UnorderedListOutlined,
    ArrowLeftOutlined,
    ReloadOutlined
} from '@ant-design/icons';
import PlagiarismDetection from '../searching/components/PlagiarismDetection.tsx';
import BatchManagement from '../searching/components/BatchManagement.tsx';
import PlagiarismResultCard from '../searching/components/PlagiarismResultCard.tsx';
import { PlagiarismProvider } from '../../context/PlagiarismContext.tsx';
import PlagiarismFloatButton from '../searching/components/PlagiarismFloatButton.tsx';
import { BookSearchResult } from '../../interface/searching.ts';
import { invokeGlobalSearchBooks } from '../../invoker/searching.ts';
import { useNavigate } from 'react-router-dom';

const { Title } = Typography;

function PlagiarismPage() {
    const [activeTab, setActiveTab] = useState<string>('batches');
    const [availableBooks, setAvailableBooks] = useState<BookSearchResult[]>([]);
    const [loading, setLoading] = useState<boolean>(false);
    const navigate = useNavigate();

    // 获取可用书籍列表
    const getAvailableBooks = () => {
        return availableBooks;
    };

    // 刷新书籍列表
    const handleRefreshBooks = async () => {
        setLoading(true);
        try {
            // 使用一个通用的搜索来获取书籍列表
            const result = await invokeGlobalSearchBooks({
                searchText: '', // 空搜索获取所有书籍
                type: null,
                pageNo: 0,
                pageSize: 100, // 获取更多书籍
                assembleId: null,
                earliestPublishDate: null,
                recentPublishDate: null,
                period: null,
                series: null,
                subjectCode: null,
                version: null,
                grade: null,
                author: null,
                publishYear: null,
                bookId: null,
                edition: null,
                brand: null
            });

            if (result && result.list) {
                setAvailableBooks(result.list);
                message.success(`已加载 ${result.list.length} 本书籍`);
            }
        } catch (error: any) {
            message.error(`加载书籍失败：${error.message}`);
        } finally {
            setLoading(false);
        }
    };

    // 页面加载时获取书籍列表
    // useEffect(() => {
    //     handleRefreshBooks();
    // }, []);

    const tabItems = [
        // {
        //     key: 'detection',
        //     label: (
        //         <Space>
        //             <BookOutlined />
        //             <span>查重设置</span>
        //         </Space>
        //     ),
        //     children: (
        //         <PlagiarismDetection
        //             availableBooks={getAvailableBooks()}
        //             onRefreshBooks={handleRefreshBooks}
        //         />
        //     )
        // },
        {
            key: 'batches',
            label: (
                <Space>
                    <UnorderedListOutlined />
                    <span>批次管理</span>
                </Space>
            ),
            children: <BatchManagement />
        },
        {
            key: 'results',
            label: (
                <Space>
                    <BarChartOutlined />
                    <span>查重结果</span>
                </Space>
            ),
            children: <PlagiarismResultCard />
        }
    ];

    return (
        <PlagiarismProvider>
            <div style={{
                height: '100vh',
                display: 'flex',
                flexDirection: 'column',
                backgroundColor: '#f5f5f5'
            }}>
                {/* 头部区域 */}
                <div style={{
                    padding: '16px 24px',
                    backgroundColor: 'white',
                    borderBottom: '1px solid #f0f0f0',
                    flexShrink: 0
                }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <div>
                            <Title level={2} style={{ margin: 0, color: '#1890ff' }}>
                                <BookOutlined style={{ marginRight: '8px' }} />
                                书本查重对比
                            </Title>
                            <div style={{ marginTop: '4px', color: '#666', fontSize: '14px' }}>
                                智能检测书籍内容相似性，支持句子级别的精确对比分析
                            </div>
                        </div>
                        <Space>
                            <Button
                                icon={<ArrowLeftOutlined />}
                                onClick={() => navigate('/global-search')}
                            >
                                返回搜索
                            </Button>
                        </Space>
                    </div>
                </div>

                {/* 主内容区域 */}
                <div style={{
                    flex: 1,
                    padding: '16px 24px',
                    overflow: 'hidden',
                    display: 'flex',
                    flexDirection: 'column'
                }}>
                    <Tabs
                        activeKey={activeTab}
                        onChange={setActiveTab}
                        items={tabItems}
                        size="large"
                        style={{
                            flex: 1,
                            display: 'flex',
                            flexDirection: 'column'
                        }}
                        tabBarStyle={{
                            marginBottom: '16px',
                            borderBottom: '2px solid #f0f0f0',
                            flexShrink: 0
                        }}
                        tabPaneStyle={{
                            flex: 1,
                            overflow: 'hidden'
                        }}
                    />
                </div>
            </div>

            {/* 浮动按钮 */}
            <PlagiarismFloatButton />
        </PlagiarismProvider>
    );
}

export default PlagiarismPage;
